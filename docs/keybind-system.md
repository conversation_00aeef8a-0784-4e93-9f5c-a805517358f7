# Keybind Configuration System

The SpeakMCP application features a comprehensive keybind configuration system that allows users to customize keyboard shortcuts for all application features.

## Overview

The keybind system consists of several key components:

- **Keybind Registry**: Centralized management of all available actions and their key combinations
- **Key Recording Interface**: Real-time key capture with visual feedback
- **Settings UI**: Comprehensive interface for managing keybinds with conflict detection
- **Configuration Integration**: Persistent storage and backward compatibility
- **Event Handler**: Refactored keyboard event processing using the registry

## Architecture

### Core Types

```typescript
interface KeyCombination {
  key: KeyCode // e.g., 'KeyA', 'Slash', 'Escape'
  modifiers: ModifierKey[] // ['ctrl', 'alt', 'shift', 'cmd']
}

interface KeybindAction {
  id: string
  name: string
  description: string
  category: KeybindCategory
  defaultKeybind?: KeyCombination
  context?: KeybindContext[]
}
```

### Available Actions

The system includes the following predefined actions:

#### Recording
- `recording.start-stop`: Start/stop voice recording (Hold Ctrl)
- `recording.start-stop-toggle`: Toggle recording on/off (Ctrl+/)
- `recording.cancel`: Cancel recording and close panel (Escape)

#### Text Input
- `text-input.show`: Open text input dialog (Ctrl+T)

#### MCP Tools
- `mcp.start-stop`: Start/stop MCP tool recording (Hold Ctrl+Alt)
- `mcp.start-stop-toggle`: Toggle MCP tool recording (Ctrl+Alt+/)

#### Agent Control
- `agent.kill-switch`: Emergency stop agent (Ctrl+Shift+Escape)

#### System
- `system.close-panel`: Close current panel (Escape)

## Usage

### For Users

1. **Access Settings**: Navigate to Settings → Keybinds
2. **Search/Filter**: Use the search bar or category filters to find specific keybinds
3. **Record Keys**: Click on any keybind field and press your desired key combination
4. **Resolve Conflicts**: Click on conflict warnings to resolve duplicate assignments
5. **Reset**: Use individual reset buttons or "Reset All" to restore defaults

### For Developers

#### Adding New Actions

```typescript
// In keybind-registry.ts
const NEW_ACTION: KeybindAction = {
  id: 'feature.action',
  name: 'Feature Action',
  description: 'Description of what this action does',
  category: 'tools',
  defaultKeybind: { key: 'KeyF', modifiers: ['ctrl'] },
  context: ['global'],
}

// Add to DEFAULT_KEYBIND_ACTIONS
export const DEFAULT_KEYBIND_ACTIONS = {
  // ... existing actions
  'feature.action': NEW_ACTION,
}
```

#### Handling Actions

```typescript
// In keyboard.ts handleKeybindAction function
case 'feature.action':
  // Implement your action logic here
  performFeatureAction()
  return true // Return true if handled
```

#### Using the Registry

```typescript
import { keybindService } from './keybind-service'

// Initialize (done automatically in main process)
keybindService.initialize()

// Check for key matches
const actionId = keybindService.matchKeybind('KeyT', {
  ctrl: true,
  alt: false,
  shift: false,
  meta: false,
})

if (actionId) {
  // Handle the matched action
  handleKeybindAction(actionId)
}
```

## Migration from Legacy System

The system automatically migrates existing keybind configurations:

- `shortcut: "hold-ctrl"` → `recording.start-stop`
- `shortcut: "ctrl-slash"` → `recording.start-stop-toggle`
- `textInputShortcut: "ctrl-t"` → `text-input.show`
- `mcpToolsShortcut: "hold-ctrl-alt"` → `mcp.start-stop`
- `agentKillSwitchHotkey: "ctrl-shift-escape"` → `agent.kill-switch`

## Configuration Storage

Keybinds are stored in the main configuration file as:

```json
{
  "keybinds": {
    "recording.start-stop": {
      "key": "ControlLeft",
      "modifiers": []
    },
    "text-input.show": {
      "key": "KeyT",
      "modifiers": ["ctrl"]
    }
  }
}
```

## Conflict Detection

The system automatically detects when multiple actions are assigned the same key combination:

- Visual warnings in the settings UI
- Clickable conflict resolution dialogs
- Automatic conflict updates when keybinds change

## Key Recording

The key recording interface provides:

- Real-time key capture with modifier display
- Visual feedback during recording (blue highlight, pulse animation)
- Escape key to cancel recording
- Automatic timeout after 10 seconds
- Cross-platform key normalization

## Platform Differences

The system handles platform-specific differences:

- **macOS**: Uses ⌘, ⌃, ⌥, ⇧ symbols for modifiers
- **Windows/Linux**: Uses Ctrl, Alt, Shift text labels
- **Key Codes**: Normalizes different key code formats

## Testing

Run the keybind system tests:

```bash
npm test keybind-registry.test.ts
```

Tests cover:
- Key combination formatting and parsing
- Registry operations (set, get, find)
- Conflict detection
- Configuration export/import
- Reset functionality

## Best Practices

1. **Action IDs**: Use descriptive, hierarchical IDs (e.g., `category.action`)
2. **Default Keybinds**: Choose non-conflicting, intuitive defaults
3. **Categories**: Group related actions logically
4. **Descriptions**: Provide clear, concise action descriptions
5. **Context**: Specify appropriate contexts for actions
6. **Testing**: Test keybind changes across different platforms

## Troubleshooting

### Common Issues

1. **Keybinds not working**: Check if accessibility permissions are granted (macOS)
2. **Conflicts not resolving**: Ensure the conflict dialog is properly handling the resolution
3. **Migration issues**: Check console for migration errors and verify legacy config format

### Debug Information

Enable debug logging to see keybind matching:

```typescript
// In keyboard.ts
console.log('Matched action:', actionId, 'for key:', keyCode, 'modifiers:', modifiers)
```

## Future Enhancements

Potential improvements to consider:

- **Context-aware keybinds**: Different shortcuts based on application state
- **Sequence shortcuts**: Multi-key sequences (e.g., Ctrl+K, Ctrl+S)
- **Global vs local**: Distinguish between global and window-specific shortcuts
- **Import/export**: Allow users to share keybind configurations
- **Profiles**: Multiple keybind profiles for different use cases
