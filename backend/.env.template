# Environment Variables Template
# Copy this file to .env.local for development

# JWT Secret - Generate a secure random 256-bit key
JWT_SECRET=your-jwt-secret-here

# Google OAuth Credentials (Web Application type)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Groq API Key
GROQ_API_KEY=your-groq-api-key

# CORS Origins (comma-separated for multiple origins)
ALLOWED_ORIGINS=*

# Database ID (get this from: wrangler d1 create speakmcp-db)
DATABASE_ID=your-d1-database-id
