<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>SpeakMCP</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' blob: assets:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:"
    />
    <script>
      function toggleThemeClass(darkMode) {
        const root = document.documentElement
        if (darkMode) {
          root.classList.add("dark")
        } else {
          root.classList.remove("dark")
        }
      }

      function initThemeClass() {
        const darkMode = window.matchMedia(
          "(prefers-color-scheme: dark)",
        ).matches
        toggleThemeClass(darkMode)
      }

      window
        .matchMedia("(prefers-color-scheme: dark)")
        .addEventListener("change", (e) => {
          toggleThemeClass(e.matches)
        })

      initThemeClass()
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
