import React, { useState, useEffect, useCallback } from 'react'
import { KeybindSettings } from '@renderer/components/keybind-settings'
import { KeybindRegistryManager } from '@shared/keybind-registry'
import { KeyCombination } from '@shared/keybind-types'
import { useConfigQuery, useSaveConfigMutation } from '@renderer/lib/query-client'
import { Config } from '@shared/types'

export function Component() {
  const configQuery = useConfigQuery()
  const saveConfigMutation = useSaveConfigMutation()
  
  const [registry, setRegistry] = useState<KeybindRegistryManager | null>(null)

  // Initialize registry when config loads
  useEffect(() => {
    if (configQuery.data) {
      const newRegistry = new KeybindRegistryManager()
      
      // Load saved keybinds if they exist
      if (configQuery.data.keybinds) {
        newRegistry.loadConfiguration(configQuery.data.keybinds)
      }
      
      setRegistry(newRegistry)
    }
  }, [configQuery.data])

  // Save configuration helper
  const saveConfig = useCallback((config: Partial<Config>) => {
    saveConfigMutation.mutate({
      config: {
        ...configQuery.data,
        ...config,
      },
    })
  }, [saveConfigMutation, configQuery.data])

  // Handle keybind changes
  const handleKeybindChange = useCallback((actionId: string, keyCombination: KeyCombination | null) => {
    if (!registry) return

    registry.setKeybind(actionId, keyCombination)
    
    // Save to config
    const updatedKeybinds = registry.exportConfiguration()
    saveConfig({ keybinds: updatedKeybinds })
  }, [registry, saveConfig])

  // Handle reset all keybinds
  const handleResetAll = useCallback(() => {
    if (!registry) return

    registry.resetAllKeybinds()
    
    // Save to config
    const updatedKeybinds = registry.exportConfiguration()
    saveConfig({ keybinds: updatedKeybinds })
    
    // Update local registry state
    setRegistry(new KeybindRegistryManager())
  }, [registry, saveConfig])

  if (!configQuery.data || !registry) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading keybind settings...</div>
      </div>
    )
  }

  return (
    <div className="grid gap-4">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Keyboard Shortcuts</h3>
        <p className="text-sm text-muted-foreground">
          Customize keyboard shortcuts for all application features. Click on any keybind field to record a new key combination.
        </p>
      </div>

      <KeybindSettings
        registry={registry}
        onKeybindChange={handleKeybindChange}
        onResetAll={handleResetAll}
      />
    </div>
  )
}
