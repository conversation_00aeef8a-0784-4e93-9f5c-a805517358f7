import React, { useState, useEffect, useCallback, useRef } from 'react'
import { cn } from '@renderer/lib/utils'
import { Button } from './button'
import { KeyCombination, ModifierKey, KeyRecordingState } from '@shared/keybind-types'
import { KeybindUtils } from '@shared/keybind-registry'

interface KeyRecorderProps {
  value?: KeyCombination | null
  onChange?: (keyCombination: KeyCombination | null) => void
  onRecordingStateChange?: (isRecording: boolean) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  allowEmpty?: boolean
}

export function KeyRecorder({
  value,
  onChange,
  onRecordingStateChange,
  placeholder = "Click to record key combination",
  disabled = false,
  className,
  allowEmpty = true,
}: KeyRecorderProps) {
  const [recordingState, setRecordingState] = useState<KeyRecordingState>({
    isRecording: false,
    recordedKeys: new Set(),
    modifiers: new Set(),
  })
  
  const inputRef = useRef<HTMLDivElement>(null)
  const recordingTimeoutRef = useRef<NodeJS.Timeout>()

  // Handle key down events during recording
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!recordingState.isRecording) return

    event.preventDefault()
    event.stopPropagation()

    const key = event.code
    const modifiers = new Set<ModifierKey>()

    // Track modifier keys
    if (event.ctrlKey) modifiers.add('ctrl')
    if (event.altKey) modifiers.add('alt')
    if (event.shiftKey) modifiers.add('shift')
    if (event.metaKey) modifiers.add(process.env.IS_MAC ? 'cmd' : 'meta')

    // Don't record modifier keys by themselves as the main key
    const isModifierKey = ['ControlLeft', 'ControlRight', 'AltLeft', 'AltRight', 
                          'ShiftLeft', 'ShiftRight', 'MetaLeft', 'MetaRight'].includes(key)

    if (!isModifierKey) {
      // We have a complete key combination
      const keyCombination: KeyCombination = {
        key: KeybindUtils.normalizeKeyCode(key),
        modifiers: Array.from(modifiers),
      }

      onChange?.(keyCombination)
      stopRecording()
    } else {
      // Update the recording state to show current modifiers
      setRecordingState(prev => ({
        ...prev,
        modifiers,
        recordedKeys: new Set([...prev.recordedKeys, key]),
      }))
    }
  }, [recordingState.isRecording, onChange])

  // Handle key up events to update modifier display
  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    if (!recordingState.isRecording) return

    const modifiers = new Set<ModifierKey>()
    if (event.ctrlKey) modifiers.add('ctrl')
    if (event.altKey) modifiers.add('alt')
    if (event.shiftKey) modifiers.add('shift')
    if (event.metaKey) modifiers.add(process.env.IS_MAC ? 'cmd' : 'meta')

    setRecordingState(prev => ({
      ...prev,
      modifiers,
    }))
  }, [recordingState.isRecording])

  // Start recording
  const startRecording = useCallback(() => {
    if (disabled) return

    setRecordingState({
      isRecording: true,
      recordedKeys: new Set(),
      modifiers: new Set(),
    })

    onRecordingStateChange?.(true)

    // Auto-stop recording after 10 seconds
    recordingTimeoutRef.current = setTimeout(() => {
      stopRecording()
    }, 10000)

    // Focus the input to capture keyboard events
    inputRef.current?.focus()
  }, [disabled, onRecordingStateChange])

  // Stop recording
  const stopRecording = useCallback(() => {
    setRecordingState(prev => ({
      ...prev,
      isRecording: false,
    }))

    onRecordingStateChange?.(false)

    if (recordingTimeoutRef.current) {
      clearTimeout(recordingTimeoutRef.current)
      recordingTimeoutRef.current = undefined
    }

    inputRef.current?.blur()
  }, [onRecordingStateChange])

  // Clear the current keybind
  const clearKeybind = useCallback(() => {
    if (allowEmpty) {
      onChange?.(null)
    }
  }, [allowEmpty, onChange])

  // Handle Escape key to cancel recording
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (recordingState.isRecording && event.key === 'Escape') {
        event.preventDefault()
        stopRecording()
      }
    }

    if (recordingState.isRecording) {
      document.addEventListener('keydown', handleKeyDown, true)
      document.addEventListener('keyup', handleKeyUp, true)
      document.addEventListener('keydown', handleEscape, true)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown, true)
      document.removeEventListener('keyup', handleKeyUp, true)
      document.removeEventListener('keydown', handleEscape, true)
    }
  }, [recordingState.isRecording, handleKeyDown, handleKeyUp])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recordingTimeoutRef.current) {
        clearTimeout(recordingTimeoutRef.current)
      }
    }
  }, [])

  // Format the current recording state for display
  const getRecordingDisplay = () => {
    if (!recordingState.isRecording) return null

    const modifierDisplay = Array.from(recordingState.modifiers)
      .map(mod => {
        const modifierMap: Record<ModifierKey, string> = {
          ctrl: process.env.IS_MAC ? '⌃' : 'Ctrl',
          cmd: '⌘',
          meta: process.env.IS_MAC ? '⌘' : 'Win',
          alt: process.env.IS_MAC ? '⌥' : 'Alt',
          shift: process.env.IS_MAC ? '⇧' : 'Shift',
        }
        return modifierMap[mod]
      })
      .join(process.env.IS_MAC ? '' : ' + ')

    return modifierDisplay || 'Press a key...'
  }

  const displayValue = recordingState.isRecording 
    ? getRecordingDisplay()
    : value 
      ? KeybindUtils.formatKeyCombination(value)
      : placeholder

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div
        ref={inputRef}
        tabIndex={0}
        onClick={startRecording}
        className={cn(
          "flex-1 min-h-[32px] px-3 py-2 text-sm border rounded-md cursor-pointer transition-colors",
          "focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent",
          recordingState.isRecording 
            ? "border-blue-500 bg-blue-50 dark:bg-blue-950 animate-pulse" 
            : "border-input bg-background hover:bg-accent",
          disabled && "opacity-50 cursor-not-allowed",
          !value && !recordingState.isRecording && "text-muted-foreground"
        )}
      >
        <div className="flex items-center justify-between">
          <span className={cn(
            recordingState.isRecording && "font-mono font-medium"
          )}>
            {displayValue}
          </span>
          {recordingState.isRecording && (
            <span className="text-xs text-muted-foreground ml-2">
              Press Esc to cancel
            </span>
          )}
        </div>
      </div>

      {value && allowEmpty && !recordingState.isRecording && (
        <Button
          variant="ghost"
          size="sm"
          onClick={clearKeybind}
          disabled={disabled}
          className="px-2 h-8"
        >
          ✕
        </Button>
      )}

      {recordingState.isRecording && (
        <Button
          variant="outline"
          size="sm"
          onClick={stopRecording}
          className="px-3 h-8"
        >
          Cancel
        </Button>
      )}
    </div>
  )
}
