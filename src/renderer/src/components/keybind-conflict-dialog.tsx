import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@renderer/components/ui/dialog'
import { Button } from '@renderer/components/ui/button'
import { Badge } from '@renderer/components/ui/badge'
import { KeybindConflict, KeybindAction } from '@shared/keybind-types'
import { KeybindUtils } from '@shared/keybind-registry'
import { AlertTriangle } from 'lucide-react'

interface KeybindConflictDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  conflict: KeybindConflict | null
  actions: Record<string, KeybindAction>
  onResolveConflict: (actionId: string, keepKeybind: boolean) => void
}

export function KeybindConflictDialog({
  open,
  onOpenChange,
  conflict,
  actions,
  onResolveConflict,
}: KeybindConflictDialogProps) {
  if (!conflict) return null

  const keyCombinationDisplay = KeybindUtils.formatKeyCombination(conflict.keyCombination)

  const handleResolve = (actionId: string, keepKeybind: boolean) => {
    onResolveConflict(actionId, keepKeybind)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            Keybind Conflict
          </DialogTitle>
          <DialogDescription>
            Multiple actions are assigned to the same key combination: <strong>{keyCombinationDisplay}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Choose which action should keep this keybind. Other actions will have their keybind removed.
          </p>

          <div className="space-y-2">
            {conflict.conflictingActions.map((actionId) => {
              const action = actions[actionId]
              if (!action) return null

              return (
                <div
                  key={actionId}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{action.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {action.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {action.description}
                    </p>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleResolve(actionId, false)}
                    >
                      Remove
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleResolve(actionId, true)}
                    >
                      Keep
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
