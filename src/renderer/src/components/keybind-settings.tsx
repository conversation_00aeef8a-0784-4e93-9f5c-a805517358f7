import React, { useState, useMemo, useCallback } from 'react'
import { Control, ControlGroup } from '@renderer/components/ui/control'
import { Input } from '@renderer/components/ui/input'
import { Button } from '@renderer/components/ui/button'
import { Badge } from '@renderer/components/ui/badge'
import { KeyRecorder } from '@renderer/components/ui/key-recorder'
import { KeybindConflictDialog } from '@renderer/components/keybind-conflict-dialog'
import {
  KeybindAction,
  KeyCombination,
  KeybindCategory,
  KeybindConflict,
  KeybindConfig
} from '@shared/keybind-types'
import { KeybindUtils, KeybindRegistryManager } from '@shared/keybind-registry'
import { Search, RotateCcw, AlertTriangle } from 'lucide-react'
import { cn } from '@renderer/lib/utils'

interface KeybindSettingsProps {
  registry: KeybindRegistryManager
  onKeybindChange: (actionId: string, keyCombination: KeyCombination | null) => void
  onResetAll: () => void
  className?: string
}

const CATEGORY_LABELS: Record<KeybindCategory, string> = {
  recording: 'Recording',
  tools: 'Tools & MCP',
  'text-input': 'Text Input',
  agent: 'Agent Control',
  navigation: 'Navigation',
  system: 'System',
}

const CATEGORY_ORDER: KeybindCategory[] = [
  'recording',
  'tools',
  'text-input',
  'agent',
  'navigation',
  'system',
]

export function KeybindSettings({
  registry,
  onKeybindChange,
  onResetAll,
  className
}: KeybindSettingsProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<KeybindCategory | 'all'>('all')
  const [conflictDialog, setConflictDialog] = useState<{
    open: boolean
    conflict: KeybindConflict | null
  }>({ open: false, conflict: null })

  const actions = registry.getActions()
  const bindings = registry.getBindings()
  const conflicts = registry.getConflicts()

  // Filter and group actions
  const filteredActions = useMemo(() => {
    const query = searchQuery.toLowerCase()

    return Object.values(actions).filter(action => {
      const matchesSearch = !query ||
        action.name.toLowerCase().includes(query) ||
        action.description.toLowerCase().includes(query) ||
        action.id.toLowerCase().includes(query)

      const matchesCategory = selectedCategory === 'all' || action.category === selectedCategory

      return matchesSearch && matchesCategory
    })
  }, [actions, searchQuery, selectedCategory])

  // Group actions by category
  const groupedActions = useMemo(() => {
    const groups: Record<KeybindCategory, KeybindAction[]> = {
      recording: [],
      tools: [],
      'text-input': [],
      agent: [],
      navigation: [],
      system: [],
    }

    filteredActions.forEach(action => {
      groups[action.category].push(action)
    })

    return groups
  }, [filteredActions])

  // Get conflicts for a specific action
  const getActionConflicts = useCallback((actionId: string): KeybindConflict | null => {
    return conflicts.find(conflict =>
      conflict.conflictingActions.includes(actionId)
    ) || null
  }, [conflicts])

  // Handle keybind change
  const handleKeybindChange = useCallback((actionId: string, keyCombination: KeyCombination | null) => {
    onKeybindChange(actionId, keyCombination)
  }, [onKeybindChange])

  // Reset individual keybind
  const handleResetKeybind = useCallback((actionId: string) => {
    registry.resetKeybind(actionId)
    const resetKeybind = registry.getKeybind(actionId)
    onKeybindChange(actionId, resetKeybind)
  }, [registry, onKeybindChange])

  // Handle conflict resolution
  const handleResolveConflict = useCallback((actionId: string, keepKeybind: boolean) => {
    const conflict = conflictDialog.conflict
    if (!conflict) return

    // Remove keybind from all conflicting actions
    conflict.conflictingActions.forEach(id => {
      if (id === actionId && keepKeybind) {
        // Keep this action's keybind
        return
      }
      // Remove keybind from other actions
      onKeybindChange(id, null)
    })

    setConflictDialog({ open: false, conflict: null })
  }, [conflictDialog.conflict, onKeybindChange])

  // Show conflict dialog when conflicts are detected
  const showConflictDialog = useCallback((conflict: KeybindConflict) => {
    setConflictDialog({ open: true, conflict })
  }, [])

  // Get available categories with action counts
  const categoryStats = useMemo(() => {
    const stats: Record<KeybindCategory, number> = {
      recording: 0,
      tools: 0,
      'text-input': 0,
      agent: 0,
      navigation: 0,
      system: 0,
    }

    Object.values(actions).forEach(action => {
      stats[action.category]++
    })

    return stats
  }, [actions])

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with search and filters */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search keybinds..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={onResetAll}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset All
          </Button>
        </div>

        {/* Category filter */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory('all')}
          >
            All ({Object.values(actions).length})
          </Button>
          {CATEGORY_ORDER.map(category => (
            categoryStats[category] > 0 && (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {CATEGORY_LABELS[category]} ({categoryStats[category]})
              </Button>
            )
          ))}
        </div>
      </div>

      {/* Conflicts warning */}
      {conflicts.length > 0 && (
        <div className="p-4 border border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200">
                Keybind Conflicts Detected
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                {conflicts.length} key combination{conflicts.length > 1 ? 's are' : ' is'} assigned to multiple actions.
                Click on a conflict below to resolve it.
              </p>
              <div className="mt-3 space-y-2">
                {conflicts.map((conflict, index) => (
                  <button
                    key={index}
                    onClick={() => showConflictDialog(conflict)}
                    className="text-left p-2 rounded border border-yellow-300 bg-yellow-100 dark:border-yellow-700 dark:bg-yellow-900 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors"
                  >
                    <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      {KeybindUtils.formatKeyCombination(conflict.keyCombination)}
                    </div>
                    <div className="text-xs text-yellow-700 dark:text-yellow-300">
                      {conflict.conflictingActions.map(id => actions[id]?.name).join(', ')}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Keybind groups */}
      <div className="space-y-6">
        {CATEGORY_ORDER.map(category => {
          const categoryActions = groupedActions[category]
          if (categoryActions.length === 0) return null

          return (
            <ControlGroup key={category} title={CATEGORY_LABELS[category]}>
              {categoryActions.map(action => {
                const currentKeybind = bindings[action.id] || null
                const conflict = getActionConflicts(action.id)
                const hasConflict = !!conflict

                return (
                  <Control
                    key={action.id}
                    label={
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{action.name}</span>
                          {hasConflict && (
                            <Badge variant="destructive" className="text-xs">
                              Conflict
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {action.description}
                        </p>
                        {hasConflict && (
                          <p className="text-xs text-red-600 dark:text-red-400">
                            Conflicts with: {conflict.conflictingActions
                              .filter(id => id !== action.id)
                              .map(id => actions[id]?.name)
                              .join(', ')}
                          </p>
                        )}
                      </div>
                    }
                    className="px-3 py-4"
                  >
                    <div className="flex items-center gap-2 min-w-[200px]">
                      <KeyRecorder
                        value={currentKeybind}
                        onChange={(keyCombination) => handleKeybindChange(action.id, keyCombination)}
                        placeholder="No keybind set"
                        className="flex-1"
                        allowEmpty={true}
                      />
                      {currentKeybind && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleResetKeybind(action.id)}
                          className="px-2 h-8 text-xs"
                        >
                          Reset
                        </Button>
                      )}
                    </div>
                  </Control>
                )
              })}
            </ControlGroup>
          )
        })}
      </div>

      {filteredActions.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>No keybinds found matching your search criteria.</p>
        </div>
      )}

      {/* Conflict resolution dialog */}
      <KeybindConflictDialog
        open={conflictDialog.open}
        onOpenChange={(open) => setConflictDialog(prev => ({ ...prev, open }))}
        conflict={conflictDialog.conflict}
        actions={actions}
        onResolveConflict={handleResolveConflict}
      />
    </div>
  )
}
