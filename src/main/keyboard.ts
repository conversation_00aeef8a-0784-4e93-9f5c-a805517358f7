import {
  getWindowRendererHand<PERSON>,
  showPanelWindowAndStartRecording,
  showPanelWindowAndStartMcpRecording,
  showPanelWindowAndShowTextInput,
  stopRecordingAndHidePanelWindow,
  stopTextInputAndHidePanelWindow,
  closeAgentModeAndHidePanelWindow,
  emergencyStopAgentMode,
  WINDOWS,
} from "./window"
import { systemPreferences } from "electron"
import { configStore } from "./config"
import { state, agentProcessManager } from "./state"
import { spawn, ChildProcess } from "child_process"
import path from "path"
import { keybindService } from "./keybind-service"

const rdevPath = path
  .join(
    __dirname,
    `../../resources/bin/speakmcp-rs${process.env.IS_MAC ? "" : ".exe"}`,
  )
  .replace("app.asar", "app.asar.unpacked")

type RdevEvent = {
  event_type: "KeyPress" | "KeyRelease"
  data: {
    key: "ControlLeft" | "BackSlash" | string
  }
  time: {
    secs_since_epoch: number
  }
}

/**
 * Handle keybind actions based on action ID
 */
const handleKeybindAction = (actionId: string): boolean => {
  const config = configStore.get()

  switch (actionId) {
    case 'recording.start-stop':
      // Hold Ctrl behavior - handled in special logic below
      return false // Let the original logic handle this

    case 'recording.start-stop-toggle':
      getWindowRendererHandlers("panel")?.startOrFinishRecording.send()
      return true

    case 'recording.cancel':
      const win = WINDOWS.get("panel")
      if (win && win.isVisible()) {
        if (state.isRecording) {
          stopRecordingAndHidePanelWindow()
        } else {
          closeAgentModeAndHidePanelWindow()
        }
      }
      return true

    case 'text-input.show':
      if (config.textInputEnabled) {
        showPanelWindowAndShowTextInput()
      }
      return true

    case 'mcp.start-stop':
      // Hold Ctrl+Alt behavior - handled in special logic below
      return false // Let the original logic handle this

    case 'mcp.start-stop-toggle':
      if (config.mcpToolsEnabled) {
        getWindowRendererHandlers("panel")?.startOrFinishMcpRecording.send()
      }
      return true

    case 'agent.kill-switch':
      if (config.agentKillSwitchEnabled && state.isAgentModeActive) {
        emergencyStopAgentMode()
      }
      return true

    case 'system.close-panel':
      const panelWin = WINDOWS.get("panel")
      if (panelWin && panelWin.isVisible()) {
        if (state.isRecording) {
          stopRecordingAndHidePanelWindow()
        } else {
          closeAgentModeAndHidePanelWindow()
        }
      }
      return true

    default:
      return false
  }
}

export const writeText = (text: string) => {
  return new Promise<void>((resolve, reject) => {
    const child: ChildProcess = spawn(rdevPath, ["write", text])

    // Register process if agent mode is active
    if (state.isAgentModeActive) {
      agentProcessManager.registerProcess(child)
    }

    let stderr = ""

    child.stderr?.on("data", (data) => {
      stderr += data.toString()
    })

    child.on("error", (error) => {
      reject(new Error(`Failed to spawn process: ${error.message}`))
    })

    child.on("close", (code) => {
      // writeText will trigger KeyPress event of the key A
      // I don't know why
      keysPressed.clear()

      if (code === 0) {
        resolve()
      } else {
        const errorMessage = `child process exited with code ${code}${stderr.trim() ? `. stderr: ${stderr.trim()}` : ""}`
        reject(new Error(errorMessage))
      }
    })
  })
}

export const getFocusedAppInfo = () => {
  return new Promise<string>((resolve, reject) => {
    const child: ChildProcess = spawn(rdevPath, ["get-focus"])

    // Register process if agent mode is active
    if (state.isAgentModeActive) {
      agentProcessManager.registerProcess(child)
    }

    let stdout = ""
    let stderr = ""

    child.stdout?.on("data", (data) => {
      stdout += data.toString()
    })

    child.stderr?.on("data", (data) => {
      stderr += data.toString()
    })

    child.on("error", (error) => {
      reject(new Error(`Failed to spawn process: ${error.message}`))
    })

    child.on("close", (code) => {
      if (code === 0) {
        resolve(stdout.trim())
      } else {
        const errorMessage = `get-focus command failed with code ${code}${stderr.trim() ? `. stderr: ${stderr.trim()}` : ""}`
        reject(new Error(errorMessage))
      }
    })
  })
}

export const restoreFocusToApp = (appInfo: string) => {
  return new Promise<void>((resolve, reject) => {
    const child: ChildProcess = spawn(rdevPath, ["restore-focus", appInfo])

    // Register process if agent mode is active
    if (state.isAgentModeActive) {
      agentProcessManager.registerProcess(child)
    }

    let stderr = ""

    child.stderr?.on("data", (data) => {
      stderr += data.toString()
    })

    child.on("error", (error) => {
      reject(new Error(`Failed to spawn process: ${error.message}`))
    })

    child.on("close", (code) => {
      if (code === 0) {
        resolve()
      } else {
        const errorMessage = `restore-focus command failed with code ${code}${stderr.trim() ? `. stderr: ${stderr.trim()}` : ""}`
        reject(new Error(errorMessage))
      }
    })
  })
}

const captureFocusBeforeRecording = async () => {
  try {
    const focusedApp = await getFocusedAppInfo()
    state.focusedAppBeforeRecording = focusedApp
  } catch (error) {
    state.focusedAppBeforeRecording = null
  }
}

export const writeTextWithFocusRestore = async (text: string) => {
  const focusedApp = state.focusedAppBeforeRecording

  if (focusedApp) {
    try {
      await restoreFocusToApp(focusedApp)

      // Small delay to ensure focus is restored before pasting
      await new Promise(resolve => setTimeout(resolve, 100))

      await writeText(text)
    } catch (error) {
      // Fallback to regular paste without focus restoration
      await writeText(text)
    }
  } else {
    await writeText(text)
  }
}

const parseEvent = (event: any) => {
  try {
    const e = JSON.parse(String(event))
    e.data = JSON.parse(e.data)
    return e as RdevEvent
  } catch {
    return null
  }
}

// keys that are currently pressed down without releasing
// excluding ctrl
// when other keys are pressed, pressing ctrl will not start recording
const keysPressed = new Map<string, number>()

const hasRecentKeyPress = () => {
  if (keysPressed.size === 0) return false

  const now = Date.now() / 1000
  return [...keysPressed.values()].some((time) => {
    // 10 seconds
    // for some weird reasons sometime KeyRelease event is missing for some keys
    // so they stay in the map
    // therefore we have to check if the key was pressed in the last 10 seconds
    return now - time < 10
  })
}

export function listenToKeyboardEvents() {
  // Initialize keybind service
  keybindService.initialize()

  let isHoldingCtrlKey = false
  let startRecordingTimer: NodeJS.Timeout | undefined
  let isPressedCtrlKey = false
  let isPressedShiftKey = false
  let isPressedAltKey = false

  // MCP tool calling state
  let isHoldingCtrlAltKey = false
  let startMcpRecordingTimer: NodeJS.Timeout | undefined
  let isPressedCtrlAltKey = false

  if (process.env.IS_MAC) {
    if (!systemPreferences.isTrustedAccessibilityClient(false)) {
      return
    }
  }

  const cancelRecordingTimer = () => {
    if (startRecordingTimer) {
      clearTimeout(startRecordingTimer)
      startRecordingTimer = undefined
    }
  }

  const cancelMcpRecordingTimer = () => {
    if (startMcpRecordingTimer) {
      clearTimeout(startMcpRecordingTimer)
      startMcpRecordingTimer = undefined
    }
  }

  const handleEvent = (e: RdevEvent) => {
    if (e.event_type === "KeyPress") {
      if (e.data.key === "ControlLeft") {
        isPressedCtrlKey = true
      }

      if (e.data.key === "ShiftLeft" || e.data.key === "ShiftRight") {
        isPressedShiftKey = true
      }

      if (e.data.key === "Alt") {
        isPressedAltKey = true
        isPressedCtrlAltKey = isPressedCtrlKey && true
      }

      // Get config once at the beginning of the function
      const config = configStore.get()

      // Check for keybind matches using the new system
      const matchedAction = keybindService.matchKeybind(e.data.key, {
        ctrl: isPressedCtrlKey,
        alt: isPressedAltKey,
        shift: isPressedShiftKey,
        meta: false, // Meta key handling would need to be added if needed
      })

      if (matchedAction) {
        const handled = handleKeybindAction(matchedAction)
        if (handled) {
          return
        }
      }

      // Special handling for hold-based shortcuts (these need custom logic)
      // Handle recording shortcut (hold Ctrl)
      const recordingKeybind = keybindService.getKeybind('recording.start-stop')
      if (recordingKeybind && recordingKeybind.key === 'ControlLeft' && recordingKeybind.modifiers.length === 0) {
        if (e.data.key === "ControlLeft") {
          if (hasRecentKeyPress()) {
            return
          }

          if (startRecordingTimer) {
            return
          }

          startRecordingTimer = setTimeout(() => {
            isHoldingCtrlKey = true
            showPanelWindowAndStartRecording()
          }, 800)
        } else {
          // Handle MCP hold shortcut (Ctrl+Alt)
          const mcpKeybind = keybindService.getKeybind('mcp.start-stop')
          if (mcpKeybind && mcpKeybind.key === 'Alt' && mcpKeybind.modifiers.includes('ctrl') &&
              e.data.key === "Alt" && isPressedCtrlKey && config.mcpToolsEnabled) {
            if (hasRecentKeyPress()) {
              return
            }

            if (startMcpRecordingTimer) {
              return
            }

            // Cancel the regular recording timer since we're starting MCP mode
            cancelRecordingTimer()

            startMcpRecordingTimer = setTimeout(() => {
              isHoldingCtrlAltKey = true
              showPanelWindowAndStartMcpRecording()
            }, 800)
          }
          } else {
            keysPressed.set(e.data.key, e.time.secs_since_epoch)
            cancelRecordingTimer()
            cancelMcpRecordingTimer()

            // when holding ctrl key, pressing any other key will stop recording
            if (isHoldingCtrlKey) {
              stopRecordingAndHidePanelWindow()
            }

            // when holding ctrl+alt key, pressing any other key will stop MCP recording
            if (isHoldingCtrlAltKey) {
              stopRecordingAndHidePanelWindow()
            }

            isHoldingCtrlKey = false
            isHoldingCtrlAltKey = false
          }
        }
      }
    } else if (e.event_type === "KeyRelease") {
      keysPressed.delete(e.data.key)

      if (e.data.key === "ControlLeft") {
        isPressedCtrlKey = false
      }

      if (e.data.key === "ShiftLeft" || e.data.key === "ShiftRight") {
        isPressedShiftKey = false
      }

      if (e.data.key === "Alt") {
        isPressedAltKey = false
        isPressedCtrlAltKey = false
      }

      if (configStore.get().shortcut === "ctrl-slash") return

      cancelRecordingTimer()
      cancelMcpRecordingTimer()

      if (e.data.key === "ControlLeft") {
        if (isHoldingCtrlKey) {
          getWindowRendererHandlers("panel")?.finishRecording.send()
        } else if (!state.isTextInputActive) {
          // Only close panel if we're not in text input mode
          stopRecordingAndHidePanelWindow()
        }

        isHoldingCtrlKey = false
      }

      if (e.data.key === "Alt") {
        if (isHoldingCtrlAltKey) {
          const panelHandlers = getWindowRendererHandlers("panel")
          panelHandlers?.finishMcpRecording.send()
        } else if (!state.isTextInputActive) {
          // Only close panel if we're not in text input mode
          stopRecordingAndHidePanelWindow()
        }

        isHoldingCtrlAltKey = false
      }
    }
  }

  const child = spawn(rdevPath, ["listen"], {})

  child.stdout.on("data", (data) => {

    const event = parseEvent(data)
    if (!event) return

    handleEvent(event)
  })
}
