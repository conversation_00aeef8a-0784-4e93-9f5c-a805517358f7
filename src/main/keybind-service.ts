import { KeybindRegistryManager, KeybindUtils } from '@shared/keybind-registry'
import { KeyCombination, KeybindConfig } from '@shared/keybind-types'
import { configStore } from './config'

/**
 * Main process keybind service for managing keyboard shortcuts
 */
export class KeybindService {
  private registry: KeybindRegistryManager
  private initialized = false

  constructor() {
    this.registry = new KeybindRegistryManager()
  }

  /**
   * Initialize the keybind service with saved configuration
   */
  initialize(): void {
    if (this.initialized) return

    const config = configStore.get()
    
    // Load saved keybinds or migrate from legacy config
    if (config.keybinds) {
      this.registry.loadConfiguration(config.keybinds)
    } else {
      // Migrate legacy keybind settings to new system
      this.migrateLegacyKeybinds(config)
    }

    this.initialized = true
  }

  /**
   * Migrate legacy keybind configuration to new system
   */
  private migrateLegacyKeybinds(config: any): void {
    const migratedKeybinds: KeybindConfig = {}

    // Migrate recording shortcut
    if (config.shortcut === 'hold-ctrl') {
      migratedKeybinds['recording.start-stop'] = { key: 'ControlLeft', modifiers: [] }
    } else if (config.shortcut === 'ctrl-slash') {
      migratedKeybinds['recording.start-stop-toggle'] = { key: 'Slash', modifiers: ['ctrl'] }
    }

    // Migrate text input shortcut
    if (config.textInputShortcut) {
      const textInputKeybind = this.parseTextInputShortcut(config.textInputShortcut)
      if (textInputKeybind) {
        migratedKeybinds['text-input.show'] = textInputKeybind
      }
    }

    // Migrate MCP tools shortcut
    if (config.mcpToolsShortcut === 'hold-ctrl-alt') {
      migratedKeybinds['mcp.start-stop'] = { key: 'Alt', modifiers: ['ctrl'] }
    } else if (config.mcpToolsShortcut === 'ctrl-alt-slash') {
      migratedKeybinds['mcp.start-stop-toggle'] = { key: 'Slash', modifiers: ['ctrl', 'alt'] }
    }

    // Migrate agent kill switch
    if (config.agentKillSwitchHotkey) {
      const killSwitchKeybind = this.parseKillSwitchHotkey(config.agentKillSwitchHotkey)
      if (killSwitchKeybind) {
        migratedKeybinds['agent.kill-switch'] = killSwitchKeybind
      }
    }

    // Load migrated keybinds
    this.registry.loadConfiguration(migratedKeybinds)

    // Save migrated configuration
    this.saveKeybinds()
  }

  /**
   * Parse legacy text input shortcut format
   */
  private parseTextInputShortcut(shortcut: string): KeyCombination | null {
    switch (shortcut) {
      case 'ctrl-t':
        return { key: 'KeyT', modifiers: ['ctrl'] }
      case 'ctrl-shift-t':
        return { key: 'KeyT', modifiers: ['ctrl', 'shift'] }
      case 'alt-t':
        return { key: 'KeyT', modifiers: ['alt'] }
      default:
        return null
    }
  }

  /**
   * Parse legacy kill switch hotkey format
   */
  private parseKillSwitchHotkey(hotkey: string): KeyCombination | null {
    switch (hotkey) {
      case 'ctrl-shift-escape':
        return { key: 'Escape', modifiers: ['ctrl', 'shift'] }
      case 'ctrl-alt-q':
        return { key: 'KeyQ', modifiers: ['ctrl', 'alt'] }
      case 'ctrl-shift-q':
        return { key: 'KeyQ', modifiers: ['ctrl', 'shift'] }
      default:
        return null
    }
  }

  /**
   * Get the keybind registry
   */
  getRegistry(): KeybindRegistryManager {
    if (!this.initialized) {
      this.initialize()
    }
    return this.registry
  }

  /**
   * Find action by key combination
   */
  findActionByKeyCombination(keyCombination: KeyCombination): string | null {
    if (!this.initialized) {
      this.initialize()
    }
    return this.registry.findActionByKeyCombination(keyCombination)
  }

  /**
   * Get keybind for an action
   */
  getKeybind(actionId: string): KeyCombination | null {
    if (!this.initialized) {
      this.initialize()
    }
    return this.registry.getKeybind(actionId)
  }

  /**
   * Set keybind for an action
   */
  setKeybind(actionId: string, keyCombination: KeyCombination | null): void {
    if (!this.initialized) {
      this.initialize()
    }
    this.registry.setKeybind(actionId, keyCombination)
    this.saveKeybinds()
  }

  /**
   * Save current keybinds to configuration
   */
  private saveKeybinds(): void {
    const currentConfig = configStore.get()
    const keybinds = this.registry.exportConfiguration()
    
    configStore.save({
      ...currentConfig,
      keybinds,
    })
  }

  /**
   * Check if a key event matches any registered keybind
   */
  matchKeybind(keyCode: string, modifiers: {
    ctrl: boolean
    alt: boolean
    shift: boolean
    meta: boolean
  }): string | null {
    if (!this.initialized) {
      this.initialize()
    }

    // Convert modifiers to our format
    const keyCombination: KeyCombination = {
      key: KeybindUtils.normalizeKeyCode(keyCode),
      modifiers: []
    }

    if (modifiers.ctrl) keyCombination.modifiers.push('ctrl')
    if (modifiers.alt) keyCombination.modifiers.push('alt')
    if (modifiers.shift) keyCombination.modifiers.push('shift')
    if (modifiers.meta) keyCombination.modifiers.push(process.env.IS_MAC ? 'cmd' : 'meta')

    return this.registry.findActionByKeyCombination(keyCombination)
  }

  /**
   * Get all current conflicts
   */
  getConflicts() {
    if (!this.initialized) {
      this.initialize()
    }
    return this.registry.getConflicts()
  }

  /**
   * Reset all keybinds to defaults
   */
  resetAllKeybinds(): void {
    if (!this.initialized) {
      this.initialize()
    }
    this.registry.resetAllKeybinds()
    this.saveKeybinds()
  }

  /**
   * Export current keybind configuration
   */
  exportConfiguration(): KeybindConfig {
    if (!this.initialized) {
      this.initialize()
    }
    return this.registry.exportConfiguration()
  }

  /**
   * Import keybind configuration
   */
  importConfiguration(config: KeybindConfig): void {
    if (!this.initialized) {
      this.initialize()
    }
    this.registry.loadConfiguration(config)
    this.saveKeybinds()
  }
}

// Global keybind service instance
export const keybindService = new KeybindService()
