/**
 * Keybind system types and interfaces
 */

export type ModifierKey = 'ctrl' | 'alt' | 'shift' | 'cmd' | 'meta'
export type KeyCode = string // e.g., 'KeyA', 'Slash', 'Escape', 'F1', etc.

export interface KeyCombination {
  key: KeyCode
  modifiers: ModifierKey[]
}

export interface KeybindAction {
  id: string
  name: string
  description: string
  category: KeybindCategory
  defaultKeybind?: KeyCombination
  context?: KeybindContext[]
}

export type KeybindCategory = 
  | 'recording'
  | 'tools'
  | 'navigation'
  | 'agent'
  | 'text-input'
  | 'system'

export type KeybindContext = 
  | 'global'
  | 'panel-visible'
  | 'recording-active'
  | 'text-input-active'
  | 'agent-active'

export interface KeybindConfig {
  [actionId: string]: KeyCombination | null // null means disabled
}

export interface KeybindConflict {
  keyCombination: KeyCombination
  conflictingActions: string[]
}

export interface KeybindRegistry {
  actions: Record<string, KeybindAction>
  bindings: KeybindConfig
  conflicts: KeybindConflict[]
}

// Utility type for key combination string representation
export type KeyCombinationString = string // e.g., 'ctrl+shift+a', 'cmd+/'

// Event types for keybind system
export interface KeybindEvent {
  actionId: string
  keyCombination: KeyCombination
  context: KeybindContext[]
  timestamp: number
}

export interface KeyRecordingState {
  isRecording: boolean
  recordedKeys: Set<string>
  modifiers: Set<ModifierKey>
  targetActionId?: string
}
