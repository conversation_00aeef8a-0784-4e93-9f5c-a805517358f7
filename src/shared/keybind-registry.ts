import {
  KeybindAction,
  KeyCombination,
  KeybindConfig,
  KeybindRegistry,
  KeybindConflict,
  KeyCombinationString,
  ModifierKey,
} from './keybind-types'

/**
 * Default keybind actions available in the application
 */
export const DEFAULT_KEYBIND_ACTIONS: Record<string, KeybindAction> = {
  // Recording actions
  'recording.start-stop': {
    id: 'recording.start-stop',
    name: 'Start/Stop Recording',
    description: 'Start or stop voice recording',
    category: 'recording',
    defaultKeybind: { key: 'ControlLeft', modifiers: [] }, // Hold Ctrl
    context: ['global'],
  },
  'recording.start-stop-toggle': {
    id: 'recording.start-stop-toggle',
    name: 'Toggle Recording',
    description: 'Toggle voice recording on/off',
    category: 'recording',
    defaultKeybind: { key: 'Slash', modifiers: ['ctrl'] }, // Ctrl+/
    context: ['global'],
  },
  'recording.cancel': {
    id: 'recording.cancel',
    name: 'Cancel Recording',
    description: 'Cancel current recording and close panel',
    category: 'recording',
    defaultKeybind: { key: 'Escape', modifiers: [] },
    context: ['global'],
  },

  // Text input actions
  'text-input.show': {
    id: 'text-input.show',
    name: 'Show Text Input',
    description: 'Open text input dialog',
    category: 'text-input',
    defaultKeybind: { key: 'KeyT', modifiers: ['ctrl'] }, // Ctrl+T
    context: ['global'],
  },

  // MCP tool actions
  'mcp.start-stop': {
    id: 'mcp.start-stop',
    name: 'Start/Stop MCP Tools',
    description: 'Start or stop MCP tool recording',
    category: 'tools',
    defaultKeybind: { key: 'Alt', modifiers: ['ctrl'] }, // Hold Ctrl+Alt
    context: ['global'],
  },
  'mcp.start-stop-toggle': {
    id: 'mcp.start-stop-toggle',
    name: 'Toggle MCP Tools',
    description: 'Toggle MCP tool recording on/off',
    category: 'tools',
    defaultKeybind: { key: 'Slash', modifiers: ['ctrl', 'alt'] }, // Ctrl+Alt+/
    context: ['global'],
  },

  // Agent actions
  'agent.kill-switch': {
    id: 'agent.kill-switch',
    name: 'Emergency Stop Agent',
    description: 'Immediately stop agent and kill all processes',
    category: 'agent',
    defaultKeybind: { key: 'Escape', modifiers: ['ctrl', 'shift'] }, // Ctrl+Shift+Escape
    context: ['global'],
  },

  // System actions
  'system.close-panel': {
    id: 'system.close-panel',
    name: 'Close Panel',
    description: 'Close the current panel or dialog',
    category: 'system',
    defaultKeybind: { key: 'Escape', modifiers: [] },
    context: ['panel-visible'],
  },
}

/**
 * Utility functions for working with key combinations
 */
export class KeybindUtils {
  /**
   * Convert a key combination to a human-readable string
   */
  static formatKeyCombination(combo: KeyCombination): string {
    const modifierMap: Record<ModifierKey, string> = {
      ctrl: process.env.IS_MAC ? '⌃' : 'Ctrl',
      cmd: '⌘',
      meta: process.env.IS_MAC ? '⌘' : 'Win',
      alt: process.env.IS_MAC ? '⌥' : 'Alt',
      shift: process.env.IS_MAC ? '⇧' : 'Shift',
    }

    const keyMap: Record<string, string> = {
      'ControlLeft': 'Ctrl',
      'ControlRight': 'Ctrl',
      'AltLeft': 'Alt',
      'AltRight': 'Alt',
      'ShiftLeft': 'Shift',
      'ShiftRight': 'Shift',
      'MetaLeft': process.env.IS_MAC ? 'Cmd' : 'Win',
      'MetaRight': process.env.IS_MAC ? 'Cmd' : 'Win',
      'Slash': '/',
      'Escape': 'Esc',
      'Space': 'Space',
      'Enter': 'Enter',
      'Backspace': 'Backspace',
      'Tab': 'Tab',
    }

    // Handle letter keys (KeyA -> A)
    const keyName = combo.key.startsWith('Key') 
      ? combo.key.slice(3) 
      : keyMap[combo.key] || combo.key

    const modifiers = combo.modifiers
      .map(mod => modifierMap[mod])
      .filter(Boolean)
      .join(process.env.IS_MAC ? '' : '+')

    return modifiers ? `${modifiers}${process.env.IS_MAC ? '' : '+'}${keyName}` : keyName
  }

  /**
   * Convert a key combination to a string for storage/comparison
   */
  static keyCombinationToString(combo: KeyCombination): KeyCombinationString {
    const sortedModifiers = [...combo.modifiers].sort()
    return `${sortedModifiers.join('+')}_${combo.key}`
  }

  /**
   * Parse a key combination string back to KeyCombination
   */
  static stringToKeyCombination(str: KeyCombinationString): KeyCombination {
    const [modifierPart, key] = str.split('_')
    const modifiers = modifierPart ? modifierPart.split('+') as ModifierKey[] : []
    return { key, modifiers }
  }

  /**
   * Check if two key combinations are equal
   */
  static areKeyCombinationsEqual(a: KeyCombination, b: KeyCombination): boolean {
    if (a.key !== b.key) return false
    if (a.modifiers.length !== b.modifiers.length) return false
    
    const sortedA = [...a.modifiers].sort()
    const sortedB = [...b.modifiers].sort()
    
    return sortedA.every((mod, i) => mod === sortedB[i])
  }

  /**
   * Normalize key codes from different sources
   */
  static normalizeKeyCode(keyCode: string): string {
    // Handle different key code formats
    const keyMap: Record<string, string> = {
      'Control': 'ControlLeft',
      'Alt': 'AltLeft',
      'Shift': 'ShiftLeft',
      'Meta': 'MetaLeft',
      'Cmd': 'MetaLeft',
    }

    return keyMap[keyCode] || keyCode
  }
}

/**
 * Keybind registry class for managing keybind actions and configurations
 */
export class KeybindRegistryManager {
  private actions: Record<string, KeybindAction>
  private bindings: KeybindConfig
  private conflicts: KeybindConflict[]

  constructor(customActions: Record<string, KeybindAction> = {}) {
    this.actions = { ...DEFAULT_KEYBIND_ACTIONS, ...customActions }
    this.bindings = this.getDefaultBindings()
    this.conflicts = []
  }

  /**
   * Get default keybind configuration from actions
   */
  private getDefaultBindings(): KeybindConfig {
    const bindings: KeybindConfig = {}
    
    Object.values(this.actions).forEach(action => {
      if (action.defaultKeybind) {
        bindings[action.id] = action.defaultKeybind
      }
    })

    return bindings
  }

  /**
   * Get all available actions
   */
  getActions(): Record<string, KeybindAction> {
    return { ...this.actions }
  }

  /**
   * Get current keybind configuration
   */
  getBindings(): KeybindConfig {
    return { ...this.bindings }
  }

  /**
   * Set keybind for an action
   */
  setKeybind(actionId: string, keyCombination: KeyCombination | null): void {
    if (!this.actions[actionId]) {
      throw new Error(`Action ${actionId} not found`)
    }

    this.bindings[actionId] = keyCombination
    this.updateConflicts()
  }

  /**
   * Get keybind for an action
   */
  getKeybind(actionId: string): KeyCombination | null {
    return this.bindings[actionId] || null
  }

  /**
   * Find action by key combination
   */
  findActionByKeyCombination(keyCombination: KeyCombination): string | null {
    for (const [actionId, binding] of Object.entries(this.bindings)) {
      if (binding && KeybindUtils.areKeyCombinationsEqual(binding, keyCombination)) {
        return actionId
      }
    }
    return null
  }

  /**
   * Update conflict detection
   */
  private updateConflicts(): void {
    const conflicts: KeybindConflict[] = []
    const keyMap = new Map<string, string[]>()

    // Group actions by key combination
    Object.entries(this.bindings).forEach(([actionId, keyCombination]) => {
      if (keyCombination) {
        const keyString = KeybindUtils.keyCombinationToString(keyCombination)
        if (!keyMap.has(keyString)) {
          keyMap.set(keyString, [])
        }
        keyMap.get(keyString)!.push(actionId)
      }
    })

    // Find conflicts
    keyMap.forEach((actionIds, keyString) => {
      if (actionIds.length > 1) {
        conflicts.push({
          keyCombination: KeybindUtils.stringToKeyCombination(keyString),
          conflictingActions: actionIds,
        })
      }
    })

    this.conflicts = conflicts
  }

  /**
   * Get current conflicts
   */
  getConflicts(): KeybindConflict[] {
    return [...this.conflicts]
  }

  /**
   * Reset keybind to default
   */
  resetKeybind(actionId: string): void {
    const action = this.actions[actionId]
    if (!action) {
      throw new Error(`Action ${actionId} not found`)
    }

    this.bindings[actionId] = action.defaultKeybind || null
    this.updateConflicts()
  }

  /**
   * Reset all keybinds to defaults
   */
  resetAllKeybinds(): void {
    this.bindings = this.getDefaultBindings()
    this.updateConflicts()
  }

  /**
   * Load keybind configuration
   */
  loadConfiguration(config: KeybindConfig): void {
    this.bindings = { ...config }
    this.updateConflicts()
  }

  /**
   * Export current configuration
   */
  exportConfiguration(): KeybindConfig {
    return { ...this.bindings }
  }
}
