import { describe, it, expect, beforeEach } from 'vitest'
import { KeybindRegistryManager, KeybindUtils } from '../shared/keybind-registry'
import { KeyCombination } from '../shared/keybind-types'

describe('KeybindUtils', () => {
  describe('formatKeyCombination', () => {
    it('should format simple key combinations', () => {
      const combo: KeyCombination = { key: 'KeyA', modifiers: [] }
      expect(KeybindUtils.formatKeyCombination(combo)).toBe('A')
    })

    it('should format key combinations with modifiers', () => {
      const combo: KeyCombination = { key: 'KeyT', modifiers: ['ctrl'] }
      expect(KeybindUtils.formatKeyCombination(combo)).toBe('Ctrl+T')
    })

    it('should format complex key combinations', () => {
      const combo: KeyCombination = { key: 'Escape', modifiers: ['ctrl', 'shift'] }
      expect(KeybindUtils.formatKeyCombination(combo)).toBe('Ctrl+Shift+Esc')
    })

    it('should handle special keys', () => {
      const combo: KeyCombination = { key: 'Slash', modifiers: ['ctrl'] }
      expect(KeybindUtils.formatKeyCombination(combo)).toBe('Ctrl+/')
    })
  })

  describe('keyCombinationToString', () => {
    it('should convert key combination to string', () => {
      const combo: KeyCombination = { key: 'KeyT', modifiers: ['ctrl', 'shift'] }
      expect(KeybindUtils.keyCombinationToString(combo)).toBe('ctrl+shift_KeyT')
    })

    it('should sort modifiers consistently', () => {
      const combo1: KeyCombination = { key: 'KeyA', modifiers: ['shift', 'ctrl'] }
      const combo2: KeyCombination = { key: 'KeyA', modifiers: ['ctrl', 'shift'] }
      expect(KeybindUtils.keyCombinationToString(combo1)).toBe(KeybindUtils.keyCombinationToString(combo2))
    })
  })

  describe('stringToKeyCombination', () => {
    it('should parse key combination string', () => {
      const str = 'ctrl+shift_KeyT'
      const result = KeybindUtils.stringToKeyCombination(str)
      expect(result.key).toBe('KeyT')
      expect(result.modifiers).toEqual(['ctrl', 'shift'])
    })

    it('should handle empty modifiers', () => {
      const str = '_KeyA'
      const result = KeybindUtils.stringToKeyCombination(str)
      expect(result.key).toBe('KeyA')
      expect(result.modifiers).toEqual([])
    })
  })

  describe('areKeyCombinationsEqual', () => {
    it('should return true for identical combinations', () => {
      const combo1: KeyCombination = { key: 'KeyT', modifiers: ['ctrl'] }
      const combo2: KeyCombination = { key: 'KeyT', modifiers: ['ctrl'] }
      expect(KeybindUtils.areKeyCombinationsEqual(combo1, combo2)).toBe(true)
    })

    it('should return true for combinations with different modifier order', () => {
      const combo1: KeyCombination = { key: 'KeyT', modifiers: ['ctrl', 'shift'] }
      const combo2: KeyCombination = { key: 'KeyT', modifiers: ['shift', 'ctrl'] }
      expect(KeybindUtils.areKeyCombinationsEqual(combo1, combo2)).toBe(true)
    })

    it('should return false for different keys', () => {
      const combo1: KeyCombination = { key: 'KeyT', modifiers: ['ctrl'] }
      const combo2: KeyCombination = { key: 'KeyA', modifiers: ['ctrl'] }
      expect(KeybindUtils.areKeyCombinationsEqual(combo1, combo2)).toBe(false)
    })

    it('should return false for different modifiers', () => {
      const combo1: KeyCombination = { key: 'KeyT', modifiers: ['ctrl'] }
      const combo2: KeyCombination = { key: 'KeyT', modifiers: ['alt'] }
      expect(KeybindUtils.areKeyCombinationsEqual(combo1, combo2)).toBe(false)
    })
  })
})

describe('KeybindRegistryManager', () => {
  let registry: KeybindRegistryManager

  beforeEach(() => {
    registry = new KeybindRegistryManager()
  })

  describe('getActions', () => {
    it('should return all available actions', () => {
      const actions = registry.getActions()
      expect(Object.keys(actions).length).toBeGreaterThan(0)
      expect(actions['recording.start-stop']).toBeDefined()
      expect(actions['text-input.show']).toBeDefined()
    })
  })

  describe('setKeybind and getKeybind', () => {
    it('should set and get keybinds', () => {
      const combo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      registry.setKeybind('recording.start-stop', combo)

      const result = registry.getKeybind('recording.start-stop')
      expect(result).toEqual(combo)
    })

    it('should allow setting null to disable keybind', () => {
      registry.setKeybind('recording.start-stop', null)

      const result = registry.getKeybind('recording.start-stop')
      expect(result).toBeNull()
    })

    it('should throw error for unknown action', () => {
      const combo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      expect(() => {
        registry.setKeybind('unknown.action', combo)
      }).toThrow('Action unknown.action not found')
    })
  })

  describe('findActionByKeyCombination', () => {
    it('should find action by key combination', () => {
      const combo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      registry.setKeybind('recording.start-stop', combo)

      const result = registry.findActionByKeyCombination(combo)
      expect(result).toBe('recording.start-stop')
    })

    it('should return null for unassigned combination', () => {
      const combo: KeyCombination = { key: 'KeyZ', modifiers: ['ctrl'] }
      const result = registry.findActionByKeyCombination(combo)
      expect(result).toBeNull()
    })
  })

  describe('conflict detection', () => {
    it('should detect conflicts when same key is assigned to multiple actions', () => {
      // First clear any existing keybinds to start fresh
      registry.setKeybind('recording.start-stop', null)
      registry.setKeybind('text-input.show', null)
      registry.setKeybind('recording.start-stop-toggle', null)
      registry.setKeybind('recording.cancel', null)
      registry.setKeybind('mcp.start-stop', null)
      registry.setKeybind('mcp.start-stop-toggle', null)
      registry.setKeybind('agent.kill-switch', null)
      registry.setKeybind('system.close-panel', null)

      const combo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }

      registry.setKeybind('recording.start-stop', combo)
      registry.setKeybind('text-input.show', combo)

      const conflicts = registry.getConflicts()
      expect(conflicts).toHaveLength(1)
      expect(conflicts[0].conflictingActions).toContain('recording.start-stop')
      expect(conflicts[0].conflictingActions).toContain('text-input.show')
    })

    it('should not detect conflicts for different key combinations', () => {
      // First clear any existing keybinds to start fresh
      registry.setKeybind('recording.start-stop', null)
      registry.setKeybind('text-input.show', null)
      registry.setKeybind('recording.start-stop-toggle', null)
      registry.setKeybind('recording.cancel', null)
      registry.setKeybind('mcp.start-stop', null)
      registry.setKeybind('mcp.start-stop-toggle', null)
      registry.setKeybind('agent.kill-switch', null)
      registry.setKeybind('system.close-panel', null)

      const combo1: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      const combo2: KeyCombination = { key: 'KeyG', modifiers: ['ctrl'] }

      registry.setKeybind('recording.start-stop', combo1)
      registry.setKeybind('text-input.show', combo2)

      const conflicts = registry.getConflicts()
      expect(conflicts).toHaveLength(0)
    })
  })

  describe('resetKeybind', () => {
    it('should reset keybind to default', () => {
      const customCombo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      registry.setKeybind('recording.start-stop', customCombo)

      registry.resetKeybind('recording.start-stop')

      const result = registry.getKeybind('recording.start-stop')
      const actions = registry.getActions()
      expect(result).toEqual(actions['recording.start-stop'].defaultKeybind)
    })
  })

  describe('resetAllKeybinds', () => {
    it('should reset all keybinds to defaults', () => {
      const customCombo: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      registry.setKeybind('recording.start-stop', customCombo)
      registry.setKeybind('text-input.show', customCombo)

      registry.resetAllKeybinds()

      const actions = registry.getActions()
      const recordingKeybind = registry.getKeybind('recording.start-stop')
      const textInputKeybind = registry.getKeybind('text-input.show')

      expect(recordingKeybind).toEqual(actions['recording.start-stop'].defaultKeybind)
      expect(textInputKeybind).toEqual(actions['text-input.show'].defaultKeybind)
    })
  })

  describe('configuration export/import', () => {
    it('should export and import configuration', () => {
      const combo1: KeyCombination = { key: 'KeyF', modifiers: ['ctrl'] }
      const combo2: KeyCombination = { key: 'KeyG', modifiers: ['alt'] }

      registry.setKeybind('recording.start-stop', combo1)
      registry.setKeybind('text-input.show', combo2)

      const config = registry.exportConfiguration()

      const newRegistry = new KeybindRegistryManager()
      newRegistry.loadConfiguration(config)

      expect(newRegistry.getKeybind('recording.start-stop')).toEqual(combo1)
      expect(newRegistry.getKeybind('text-input.show')).toEqual(combo2)
    })
  })
})
