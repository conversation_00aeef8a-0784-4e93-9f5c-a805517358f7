<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <!-- App Sandbox is required for Mac App Store -->
    <key>com.apple.security.app-sandbox</key>
    <true/>

    <!-- Inherit parent's sandbox -->
    <key>com.apple.security.inherit</key>
    <true/>

    <!-- Allow JIT for child processes (required for Electron) -->
    <key>com.apple.security.cs.allow-jit</key>
    <true/>

    <!-- Allow unsigned executable memory for child processes (required for Electron) -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>

    <!-- Allow DYLD environment variables for child processes (required for Electron) -->
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
  </dict>
</plist>
