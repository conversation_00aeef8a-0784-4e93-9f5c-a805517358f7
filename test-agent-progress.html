<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Progress Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-case {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Agent Progress Component Test</h1>
        <div id="test-cases"></div>
    </div>

    <script type="text/babel">
        // Mock the AgentProgress component logic
        const extractTerminalResult = (content) => {
            if (!content) return null
            
            const terminalOutputMatch = content.match(/Terminal Output:\s*```\s*([\s\S]*?)\s*```/i)
            if (terminalOutputMatch) {
                const output = terminalOutputMatch[1].trim()
                const lines = output.split('\n').map(line => line.trim()).filter(line => line)
                
                for (let i = lines.length - 1; i >= 0; i--) {
                    const line = lines[i]
                    if (line.includes('$') || line.includes('bash-') || line.includes('zsh') || 
                        line.includes('interactive shell') || line.includes('chsh') ||
                        line.includes('support.apple.com')) {
                        continue
                    }
                    
                    if (/^\d+$/.test(line)) {
                        return line
                    }
                    
                    if (line.length > 0 && line.length < 100) {
                        return line
                    }
                }
            }
            
            return null
        }

        const extractKeyResults = (content) => {
            if (!content) return { summary: "", details: [], type: 'generic' }
            
            const terminalResult = extractTerminalResult(content)
            if (terminalResult) {
                if (/^\d+$/.test(terminalResult)) {
                    const lowerContent = content.toLowerCase()
                    if (lowerContent.includes('desktop') || lowerContent.includes('~/desktop')) {
                        return {
                            summary: `${terminalResult} files on desktop`,
                            details: [],
                            type: 'info'
                        }
                    } else if (lowerContent.includes('ls') && lowerContent.includes('wc -l')) {
                        return {
                            summary: `${terminalResult} items found`,
                            details: [],
                            type: 'info'
                        }
                    } else {
                        return {
                            summary: `Result: ${terminalResult}`,
                            details: [],
                            type: 'info'
                        }
                    }
                }
                return {
                    summary: terminalResult,
                    details: [],
                    type: 'info'
                }
            }
            
            return { summary: content.substring(0, 100), details: [], type: 'generic' }
        }

        // Test cases
        const testCases = [
            {
                title: "Desktop File Count - Before (Unhelpful)",
                finalContent: "Closing the terminal session",
                toolResult: null,
                expected: "Should show generic message"
            },
            {
                title: "Desktop File Count - After (Improved)",
                finalContent: "Closing the terminal session",
                toolResult: `Command executed: ls ~/Desktop | wc -l

Terminal Output:
\`\`\`
The default interactive shell is now zsh.
To update your account to use zsh, please run \`chsh -s /bin/zsh\`.
For more details, please visit https://support.apple.com/kb/HT208050.
bash-3.2$ ls ~/Desktop | wc -l
      18
bash-3.2$
\`\`\``,
                expected: "Should show '18 files on desktop'"
            },
            {
                title: "File Creation Result",
                finalContent: "Task completed successfully",
                toolResult: `Command executed: touch ~/Documents/test.txt

Terminal Output:
\`\`\`
bash-3.2$ touch ~/Documents/test.txt
bash-3.2$
\`\`\``,
                expected: "Should show meaningful result"
            },
            {
                title: "Directory Listing Count",
                finalContent: "Finished counting",
                toolResult: `Command executed: ls /usr/bin | wc -l

Terminal Output:
\`\`\`
bash-3.2$ ls /usr/bin | wc -l
     1247
bash-3.2$
\`\`\``,
                expected: "Should show '1247 items found'"
            }
        ]

        const TestCase = ({ testCase }) => {
            // Simulate the improved logic
            const toolResults = testCase.toolResult ? extractKeyResults(testCase.toolResult) : null
            const finalResults = extractKeyResults(testCase.finalContent)
            
            // Prioritize tool results over final content
            let displayResult = finalResults.summary
            if (toolResults && toolResults.summary && toolResults.summary.length > displayResult.length) {
                displayResult = toolResults.summary
            }
            
            return (
                <div className="test-case">
                    <div className="test-title">{testCase.title}</div>
                    <div><strong>Final Content:</strong> "{testCase.finalContent}"</div>
                    {testCase.toolResult && (
                        <div><strong>Tool Result:</strong> {testCase.toolResult.substring(0, 100)}...</div>
                    )}
                    <div><strong>Expected:</strong> {testCase.expected}</div>
                    <div style={{
                        marginTop: '10px', 
                        padding: '8px', 
                        backgroundColor: displayResult.includes('files on desktop') || displayResult.includes('items found') ? '#e8f5e8' : '#fff3cd',
                        border: '1px solid ' + (displayResult.includes('files on desktop') || displayResult.includes('items found') ? '#28a745' : '#ffc107'),
                        borderRadius: '4px'
                    }}>
                        <strong>Actual Result:</strong> "{displayResult}"
                    </div>
                </div>
            )
        }

        const App = () => {
            return (
                <div>
                    {testCases.map((testCase, index) => (
                        <TestCase key={index} testCase={testCase} />
                    ))}
                </div>
            )
        }

        ReactDOM.render(<App />, document.getElementById('test-cases'))
    </script>
</body>
</html>
